# 更新日志

## v2.0 - 2024年5月28日

### 新增功能

#### 1. Timer工作模式枚举定义
- 添加 `timer_mode_t` 枚举类型，定义Timer工作模式：
  - `TIMER_MODE_0` - 13位定时器/计数器
  - `TIMER_MODE_1` - 16位定时器/计数器
  - `TIMER_MODE_2` - 8位自动重装定时器/计数器
  - `TIMER_MODE_3` - 双8位定时器(仅Timer0)

- 延时函数直接使用 `DELAY_TIMER_SELECT` 宏定义选择定时器，无需额外的枚举类型

#### 2. 多目标编译支持
- 每个demo都可以编译成独立的hex文件
- 添加构建脚本：
  - `build.sh` - SDCC环境构建脚本
  - `build_keil.bat` - Keil环境构建脚本
- 支持的编译目标：
  - `main.hex` - 主程序
  - `gpio_demo.hex` - GPIO演示程序
  - `uart_demo.hex` - 串口演示程序
  - `timer_demo.hex` - 定时器演示程序

#### 3. FOSC动态配置增强
- 所有时序相关计算都基于CONFIG.h中的FOSC设置
- `delay_ms_loop` 根据FOSC动态计算循环次数
- `delay_ms_timer` 根据FOSC动态计算定时器初值
- `UART_Init` 根据FOSC和UART_BAUD动态计算波特率

#### 4. 延时定时器选择
- 添加 `DELAY_TIMER_SELECT` 配置项
- 支持选择Timer0/Timer1/Timer2用于延时函数
- 自动处理定时器冲突检测

#### 5. 编译器兼容性改进
- 完善SDCC和Keil C51编译器兼容性
- 自动检测编译器类型并使用相应的头文件定义
- 修复中断函数语法差异
- 修复寄存器位定义差异

### 修复问题

#### 1. 编译错误修复
- 修复SDCC环境下的头文件包含问题
- 修复中断函数语法兼容性问题
- 修复寄存器位名称差异问题
- 添加缺失的GPIO函数实现

#### 2. 警告消除
- 优化条件编译逻辑，减少编译警告
- 修复类型转换警告
- 优化代码结构，消除无用代码警告

### 代码结构优化

#### 1. 头文件组织
- 统一头文件包含路径
- 添加编译器兼容性检查
- 优化宏定义结构

#### 2. 函数接口改进
- Timer初始化函数使用枚举参数
- 添加LED操作函数
- 统一函数命名规范

#### 3. 配置文件增强
- 添加延时定时器选择配置
- 完善配置项注释
- 添加默认值保护

### 文档更新

#### 1. README.md
- 添加Timer枚举使用说明
- 添加多目标编译说明
- 添加构建脚本使用方法
- 更新项目结构说明

#### 2. 代码注释
- 完善函数注释
- 添加参数说明
- 添加使用示例

#### 3. 示例代码
- 添加Timer枚举使用示例
- 更新demo代码，支持独立编译
- 添加编译器兼容性示例

### 构建系统

#### 1. SDCC构建脚本 (build.sh)
- 自动编译所有库文件
- 支持多目标编译
- 自动生成hex文件
- 彩色输出和进度显示

#### 2. Keil构建脚本 (build_keil.bat)
- 支持Keil uVision批量编译
- 自动切换编译目标
- 生成独立hex文件
- 错误日志记录

#### 3. Makefile
- 支持make命令编译
- 依赖关系管理
- 清理和帮助功能

### 兼容性

#### 支持的编译器
- SDCC 4.0+
- Keil C51 v9.0+

#### 支持的开发环境
- VSCode + EIDE
- Keil uVision 5

#### 支持的单片机
- STC89C52RC
- STC89C51RC
- AT89C52
- 其他8051兼容单片机

---

## v1.0 - 初始版本

### 基础功能
- 基础SDCC模板项目
- 基本的GPIO、UART、Timer功能
- 简单的延时函数
- 基础配置文件
