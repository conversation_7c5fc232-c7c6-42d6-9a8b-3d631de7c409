{"name": "STC89C52RC-template", "type": "C51", "dependenceList": [], "srcDirs": ["src", "inc"], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": []}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "e55017e78f24f34e0cda10b97cffd3ff"}, "targets": {"Debug": {"excludeList": [], "toolchain": "SDCC", "compileConfig": {"options": "null"}, "uploader": "Custom", "uploadConfig": {"bin": "", "commandLine": "python ./tools/stcflash.py -p ${port} \"${programFile}\"", "eraseChipCommand": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["inc"], "libList": [], "defineList": []}, "builderOptions": {"SDCC": {"version": 3, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"device": "mcs51", "optimize-type": "speed", "misc-controls": "--iram-size 256 --xram-size 0 --code-size 8192"}, "c/cpp-compiler": {"language-c": "c99", "misc-controls": "-D UART_ENABLE=1"}, "asm-compiler": {}, "linker": {"$mainFileName": "main", "output-format": "hex"}}}}}, "version": "3.5"}