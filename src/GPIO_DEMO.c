/**
 * GPIO示例：LED闪烁
 *
 * 本示例演示如何使用GPIO库控制LED闪烁
 */

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "../inc/config.h"
#include "../lib/gpio.h"
#include "../lib/delay.h"

void GPIO_Demo(void)
{
    // 初始化LED引脚
    LED_Init();

    while(1)
    {
        // 翻转LED状态
        GPIO_Toggle(LED_PIN);
        // 延时500ms
        delay_ms(500);
    }
}

#ifdef RUN_GPIO_DEMO
/**
 * @brief 主函数 - 仅在独立编译时使用
 */
void main(void)
{
    GPIO_Demo();
}
#endif