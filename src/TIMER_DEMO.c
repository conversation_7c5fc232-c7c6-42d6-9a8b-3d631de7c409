/**
 * 定时器示例：使用定时器中断控制LED闪烁
 *
 * 本示例演示如何使用定时器中断实现LED闪烁
 */

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "config.h"
#include "timer.h"
#include "gpio.h"

/* LED 配置 */
#ifndef LED_PIN
#ifdef __SDCC
#define LED_PIN         P0_0    // LED引脚
#else
#define LED_PIN         P0^0     // LED引脚
#endif
#endif

// 定时器0中断服务函数
#ifdef __SDCC
void Timer0_ISR() __interrupt(1)
#else
void Timer0_ISR() interrupt 1
#endif
{
    // 翻转LED状态
    GPIO_Toggle(LED_PIN);
}

void TIMER_Demo(void)
{
    // 初始化LED引脚
    LED_Init();

    // 初始化定时器0，模式1，500ms中断一次
    Timer0_Init(TIMER_MODE_1, 500);

    // 启动定时器0
    Timer0_Start();

    // 开启全局中断
    EA = 1;

    while(1)
    {
        // 主循环中不需要做任何事情，LED闪烁由定时器中断控制
    }
}

/**
 * @brief 主函数 - 仅在独立编译时使用
 */
void main(void)
{
    TIMER_Demo();
}