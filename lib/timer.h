#ifndef __TIMER_H__
#define __TIMER_H__

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "config.h"

/**
 * @brief 初始化定时器0
 * @param mode 工作模式(0-3)
 * @param ms 定时时间(毫秒)
 */
void Timer0_Init(unsigned char mode, unsigned int ms);

/**
 * @brief 初始化定时器1
 * @param mode 工作模式(0-3)
 * @param ms 定时时间(毫秒)
 */
void Timer1_Init(unsigned char mode, unsigned int ms);

/**
 * @brief 启动定时器0
 */
#define Timer0_Start() (TR0 = 1)

/**
 * @brief 停止定时器0
 */
#define Timer0_Stop() (TR0 = 0)

/**
 * @brief 启动定时器1
 */
#define Timer1_Start() (TR1 = 1)

/**
 * @brief 停止定时器1
 */
#define Timer1_Stop() (TR1 = 0)

#endif