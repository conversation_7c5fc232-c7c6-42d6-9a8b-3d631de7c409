#include "delay.h"

/**
 * @brief 使用定时器的毫秒延时函数
 * @param ms 延时的毫秒数
 */
void delay_ms_timer(unsigned int ms) {
    unsigned int i;
    unsigned long counts;
    unsigned char timer_select = DELAY_TIMER_SELECT;

    for(i = 0; i < ms; i++) {
        // 计算1ms所需的计数值
        // 对于12分频的8051，计数频率 = FOSC/12
        counts = FOSC / 12000;  // 1ms = 1000us, 所以除以12000
        if(counts > 65536) counts = 65536;  // 16位定时器最大值限制
        counts = 65536 - counts;

        if(timer_select == 0) {
            // 使用定时器0
            TMOD &= 0xF0;   // 清除定时器0的控制位
            TMOD |= 0x01;   // 设置定时器0为模式1（16位定时器）
            TH0 = (unsigned char)(counts >> 8);
            TL0 = (unsigned char)counts;
            TF0 = 0;        // 清除溢出标志
            TR0 = 1;        // 启动定时器0
            while(!TF0);    // 等待溢出
            TR0 = 0;        // 关闭定时器
            TF0 = 0;        // 清除溢出标志
        } else if(timer_select == 1) {
            // 使用定时器1
            TMOD &= 0x0F;   // 清除定时器1的控制位
            TMOD |= 0x10;   // 设置定时器1为模式1（16位定时器）
            TH1 = (unsigned char)(counts >> 8);
            TL1 = (unsigned char)counts;
            TF1 = 0;        // 清除溢出标志
            TR1 = 1;        // 启动定时器1
            while(!TF1);    // 等待溢出
            TR1 = 0;        // 关闭定时器
            TF1 = 0;        // 清除溢出标志
        }
#ifdef T2CON
        else if(timer_select == 2) {
            // 使用定时器2（仅8052支持）
            T2CON &= 0xFC;  // 清除定时器2控制位
            T2CON |= 0x00;  // 16位自动重装模式
            RCAP2H = (unsigned char)(counts >> 8);
            RCAP2L = (unsigned char)counts;
            TH2 = RCAP2H;
            TL2 = RCAP2L;
            TF2 = 0;        // 清除溢出标志
            TR2 = 1;        // 启动定时器2
            while(!TF2);    // 等待溢出
            TR2 = 0;        // 关闭定时器
            TF2 = 0;        // 清除溢出标志
        }
#endif
        else {
            // 默认使用定时器0
            TMOD &= 0xF0;
            TMOD |= 0x01;
            TH0 = (unsigned char)(counts >> 8);
            TL0 = (unsigned char)counts;
            TF0 = 0;
            TR0 = 1;
            while(!TF0);
            TR0 = 0;
            TF0 = 0;
        }
    }
}

/**
 * @brief 使用循环的毫秒延时函数
 * @param ms 延时的毫秒数
 */
void delay_ms_loop(unsigned int ms) {
    unsigned int i, j;
    unsigned int loop_count;

    // 根据FOSC动态计算循环次数
    // 经验公式：loop_count ≈ FOSC / 97656 (大约值，需要根据实际情况调整)
    // 对于12MHz: 12000000/97656 ≈ 123
    // 对于11.0592MHz: 11059200/97656 ≈ 113
    loop_count = (unsigned int)(FOSC / 97656UL);
    if(loop_count < 10) loop_count = 10;  // 最小值保护

    for(i = 0; i < ms; i++) {
        for(j = 0; j < loop_count; j++);
    }
}

/**
 * @brief 统一的延时函数接口，根据配置选择实现方式
 * @param ms 延时的毫秒数
 */
void delay_ms(unsigned int ms) {
#if DELAY_USE_TIMER
    delay_ms_timer(ms);
#else
    delay_ms_loop(ms);
#endif
}