#ifndef __TIMER_H__
#define __TIMER_H__

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "config.h"

/**
 * @brief Timer工作模式枚举
 */
typedef enum {
    TIMER_MODE_0 = 0,   // 模式0: 13位定时器/计数器
    TIMER_MODE_1 = 1,   // 模式1: 16位定时器/计数器
    TIMER_MODE_2 = 2,   // 模式2: 8位自动重装定时器/计数器
    TIMER_MODE_3 = 3    // 模式3: 双8位定时器(仅Timer0)
} timer_mode_t;

/**
 * @brief Timer选择枚举
 */
typedef enum {
    TIMER_0 = 0,        // 定时器0
    TIMER_1 = 1,        // 定时器1
    TIMER_2 = 2         // 定时器2 (仅8052支持)
} timer_select_t;

/**
 * @brief 初始化定时器0
 * @param mode 工作模式
 * @param ms 定时时间(毫秒)
 */
void Timer0_Init(timer_mode_t mode, unsigned int ms);

/**
 * @brief 初始化定时器1
 * @param mode 工作模式
 * @param ms 定时时间(毫秒)
 */
void Timer1_Init(timer_mode_t mode, unsigned int ms);

/**
 * @brief 通用定时器初始化函数
 * @param timer 定时器选择
 * @param mode 工作模式
 * @param ms 定时时间(毫秒)
 */
void Timer_Init(timer_select_t timer, timer_mode_t mode, unsigned int ms);

/**
 * @brief 启动定时器0
 */
#define Timer0_Start() (TR0 = 1)

/**
 * @brief 停止定时器0
 */
#define Timer0_Stop() (TR0 = 0)

/**
 * @brief 启动定时器1
 */
#define Timer1_Start() (TR1 = 1)

/**
 * @brief 停止定时器1
 */
#define Timer1_Stop() (TR1 = 0)

#endif