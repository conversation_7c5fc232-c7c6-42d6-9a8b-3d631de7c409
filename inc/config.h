#ifndef __CONFIG_H__
#define __CONFIG_H__

/* 系统时钟配置 */
#ifndef FOSC
#define FOSC        12000000L   // 系统时钟频率(Hz)
#endif

/* 串口配置 */
#ifndef UART_ENABLE
#define UART_ENABLE     1       // 是否启用串口
#endif

#ifndef UART_BAUD
#define UART_BAUD       9600    // 串口波特率
#endif

#ifndef UART_INPUT
#define UART_INPUT      0       // 是否启用串口输入
#endif

/* GPIO配置 */
#ifndef LED_PORT
#define LED_PORT        P0      // LED所在端口
#endif

#ifndef LED_PIN
#define LED_PIN         P00     // LED引脚
#endif

/* 定时器配置 */
#ifndef TIMER0_ENABLE
#define TIMER0_ENABLE   1       // 是否启用定时器0
#endif

#ifndef TIMER1_ENABLE
#define TIMER1_ENABLE   1       // 是否启用定时器1
#endif

/* 中断配置 */
#ifndef INT_PRIORITY
#define INT_PRIORITY    0       // 是否启用中断优先级
#endif

/* 其他系统配置 */
#ifndef DELAY_USE_TIMER
#define DELAY_USE_TIMER 1       // 延时函数是否使用定时器(1)或循环(0)
#endif

#endif /* __CONFIG_H__ */