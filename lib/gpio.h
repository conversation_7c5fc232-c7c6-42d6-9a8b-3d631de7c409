#ifndef __GPIO_H__
#define __GPIO_H__

#include <reg52.h>
#include "config.h"

/**
 * @brief 设置引脚为高电平
 * @param pin 引脚名称，如P00, P01等
 */
#define GPIO_SetHigh(pin) (pin = 1)

/**
 * @brief 设置引脚为低电平
 * @param pin 引脚名称，如P00, P01等
 */
#define GPIO_SetLow(pin) (pin = 0)

/**
 * @brief 翻转引脚电平
 * @param pin 引脚名称，如P00, P01等
 */
#define GPIO_Toggle(pin) (pin = !pin)

/**
 * @brief 读取引脚电平
 * @param pin 引脚名称，如P00, P01等
 * @return 引脚电平(0或1)
 */
#define GPIO_Read(pin) (pin)

#endif