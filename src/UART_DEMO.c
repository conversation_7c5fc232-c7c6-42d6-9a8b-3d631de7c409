/**
 * 串口示例：发送和接收数据
 *
 * 本示例演示如何使用串口发送数据和接收数据
 */

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include <stdio.h>
#include "../inc/config.h"
#include "../lib/uart.h"
#include "../lib/delay.h"

void UART_Demo(void)
{
    // 初始化串口
    UART_Init();

    // 发送欢迎信息
    printf("UART Demo Started!\r\n");
    UART_SendString("Please input a character...\r\n");

    while(1)
    {
#if UART_ENABLE && UART_INPUT
        if(RI) {
            // 接收到数据
            unsigned char c = UART_ReceiveByte();
            // 回显接收到的字符
            printf("Received: %c (0x%02X)\r\n", c, c);
        }
#endif
        // 每隔1秒发送一条消息
        UART_SendString("System running...\r\n");
        delay_ms(1000);
    }
}

#ifdef RUN_UART_DEMO
/**
 * @brief 主函数 - 仅在独立编译时使用
 */
void main(void)
{
    UART_Demo();
}
#endif