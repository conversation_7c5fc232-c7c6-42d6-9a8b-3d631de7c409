#include "delay.h"

/**
 * @brief 使用定时器的毫秒延时函数
 * @param ms 延时的毫秒数
 */
void delay_ms_timer(unsigned int ms) {
    unsigned int i;
    for(i = 0; i < ms; i++) {
        TMOD &= 0xF0;   // 清除定时器0的控制位
        TMOD |= 0x01;   // 设置定时器0为模式1（16位定时器）

        // FOSC时钟下，计算1ms所需的计数值
        // 12MHz时，TH0:TL0 = 65536 - 1000 = 64536 = 0xFC18
        TH0 = 0xFC;
        TL0 = 0x18;

        TF0 = 0;        // 清除溢出标志
        TR0 = 1;        // 启动定时器0

        while(!TF0);    // 等待溢出
        TR0 = 0;        // 关闭定时器
        TF0 = 0;        // 清除溢出标志
    }
}

/**
 * @brief 使用循环的毫秒延时函数
 * @param ms 延时的毫秒数
 */
void delay_ms_loop(unsigned int ms) {
    unsigned int i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 123; j++);  // 12MHz下大致为1ms的空循环
    }
}

/**
 * @brief 统一的延时函数接口，根据配置选择实现方式
 * @param ms 延时的毫秒数
 */
void delay_ms(unsigned int ms) {
#if DELAY_USE_TIMER
    delay_ms_timer(ms);
#else
    delay_ms_loop(ms);
#endif
}