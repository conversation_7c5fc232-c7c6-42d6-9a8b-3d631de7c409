/**
 * @file timer_enum_example.c
 * @brief Timer枚举使用示例
 * <AUTHOR> Name
 * @date 2024
 */

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "../inc/config.h"
#include "../lib/timer.h"
#include "../lib/gpio.h"

// 定时器0中断服务函数
#ifdef __SDCC
void Timer0_ISR() __interrupt(1)
#else
void Timer0_ISR() interrupt 1
#endif
{
    // 翻转LED状态
    GPIO_Toggle(LED_PIN);
}

/**
 * @brief Timer枚举使用示例
 */
void Timer_Enum_Example(void)
{
    // 初始化LED引脚
    LED_Init();
    
    // 示例1：使用Timer0，模式1，1000ms中断一次
    Timer_Init(TIMER_0, TIMER_MODE_1, 1000);
    
    // 示例2：使用Timer1，模式2，500ms中断一次
    // Timer_Init(TIMER_1, TIMER_MODE_2, 500);
    
    // 启动定时器0
    Timer0_Start();
    
    // 开启全局中断
    EA = 1;
    
    while(1)
    {
        // 主循环中不需要做任何事情，LED闪烁由定时器中断控制
        // 可以在这里添加其他任务
    }
}

#ifdef RUN_TIMER_ENUM_EXAMPLE
/**
 * @brief 主函数 - 仅在独立编译时使用
 */
void main(void)
{
    Timer_Enum_Example();
}
#endif
