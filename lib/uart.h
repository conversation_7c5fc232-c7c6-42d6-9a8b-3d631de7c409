#ifndef __UART_H__
#define __UART_H__

#include <stdio.h>
#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "../inc/config.h"

/**
 * @brief 初始化串口
 */
void UART_Init(void);

/**
 * @brief 发送一个字节
 * @param byte 要发送的字节
 */
void UART_SendByte(unsigned char byte);

/**
 * @brief 发送字符串
 * @param str 要发送的字符串
 */
void UART_SendString(char *str);

#if UART_INPUT
/**
 * @brief 接收一个字节(阻塞)
 * @return 接收到的字节
 */
unsigned char UART_ReceiveByte(void);
#endif
#endif
