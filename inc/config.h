#ifndef __CONFIG_H__
#define __CONFIG_H__

/* 系统时钟配置 */
#ifndef FOSC
#define FOSC        11059200
#endif

/* MCUN 模式配置: 12T 还是 6T, STC-ISP下载程序时设置 */
#ifndef MCU_MODE
#define MCU_MODE        12  
#endif

/* 启用串口调试 */
#ifndef ENABLE_UART_DEBUG
#define ENABLE_UART_DEBUG     1     
#endif

#ifndef UART_BAUD
#define UART_BAUD       9600    // 串口波特率
#endif

/* 定时器配置 */
#ifndef TIMER0_ENABLE
#define TIMER0_ENABLE   1       // 是否启用定时器0
#endif

#ifndef TIMER1_ENABLE
#define TIMER1_ENABLE   1       // 是否启用定时器1
#endif

/* 中断配置 */
#ifndef INT_PRIORITY
#define INT_PRIORITY    0       // 是否启用中断优先级
#endif

/* 延时配置 */
#ifndef DELAY_USE_TIMER
#define DELAY_USE_TIMER 1       // 延时函数是否使用定时器(1)或循环(0)
#endif

#ifndef DELAY_TIMER_SELECT
#define DELAY_TIMER_SELECT 0    // 延时使用的定时器选择(0=Timer0, 1=Timer1, 2=Timer2)
#endif

/* 其他系统配置 */

#endif /* __CONFIG_H__ */