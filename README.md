# STC89C52RC 模板项目

这是一个兼容SDCC和Keil C51编译器的STC89C52RC单片机模板项目。

## 项目特性

### 1. 编译器兼容性
- **SDCC编译器**: 支持VSCode + EIDE开发环境
- **Keil C51编译器**: 支持Keil uVision 5开发环境
- 自动检测编译器类型并使用相应的头文件定义

### 2. 动态配置支持
- **FOSC配置**: 所有时序相关计算都基于CONFIG.h中的FOSC设置
- **UART波特率配置**: 通过CONFIG.h中的UART_BAUD设置
- **延时定时器选择**: 支持选择Timer0/Timer1/Timer2用于延时函数

### 3. 智能计算
- **delay_ms_loop**: 根据FOSC动态计算循环次数
- **delay_ms_timer**: 根据FOSC动态计算定时器初值，支持多定时器选择
- **UART_Init**: 根据FOSC和UART_BAUD动态计算波特率

### 4. Timer工作模式枚举
- **timer_mode_t**: 定义Timer工作模式枚举（MODE_0/1/2/3）
- **timer_select_t**: 定义Timer选择枚举（TIMER_0/1/2）
- **Timer_Init**: 通用定时器初始化函数，支持枚举参数

### 5. 多目标编译支持
- **独立Demo编译**: 每个demo都可以编译成独立的hex文件
- **SDCC构建脚本**: build.sh支持一键编译所有目标
- **Keil构建脚本**: build_keil.bat支持Keil环境下的多目标编译

## 配置说明

### CONFIG.h 主要配置项

```c
// 系统时钟配置
#define FOSC        12000000L   // 系统时钟频率(Hz)

// 串口配置
#define UART_BAUD       9600    // 串口波特率

// 延时配置
#define DELAY_USE_TIMER 1       // 延时函数是否使用定时器(1)或循环(0)
#define DELAY_TIMER_SELECT 0    // 延时使用的定时器选择(0=Timer0, 1=Timer1, 2=Timer2)
```

### 支持的时钟频率
- 12MHz (默认)
- 11.0592MHz
- 24MHz
- 其他频率（需要在main.c中定义FOSC）

### 支持的波特率
- 9600 (默认)
- 115200
- 57600
- 38400
- 其他标准波特率

## 开发环境

### VSCode + EIDE
1. 打开 `STC89C52RC-template.code-workspace`
2. 使用SDCC编译器
3. 编译输出在 `build/Debug/` 目录

### Keil uVision 5
1. 打开 `STC89C52RC-template.uvproj`
2. 使用Keil C51编译器
3. 编译输出在 `build/keil/` 目录

## 项目结构

```
├── inc/                    # 头文件目录
│   ├── config.h           # 系统配置文件
│   └── reg52.h            # 寄存器定义（兼容SDCC和Keil）
├── lib/                    # 库文件目录
│   ├── delay.c/h          # 延时函数库
│   ├── uart.c/h           # 串口函数库
│   ├── gpio.c/h           # GPIO函数库
│   └── timer.c/h          # 定时器函数库
├── src/                    # 源文件目录
│   ├── main.c             # 主程序
│   ├── GPIO_DEMO.c        # GPIO示例
│   ├── UART_DEMO.c        # 串口示例
│   └── TIMER_DEMO.c       # 定时器示例
├── build/                  # 编译输出目录
│   ├── Debug/             # EIDE/SDCC输出
│   └── keil/              # Keil输出
└── tools/                  # 工具目录
    └── stcflash.py        # STC下载工具
```

## 使用示例

### 1. 修改系统时钟
在main.c中添加：
```c
#define FOSC 11059200L  // 使用11.0592MHz晶振
#include "config.h"
```

### 2. 修改串口波特率
在main.c中添加：
```c
#define UART_BAUD 115200  // 使用115200波特率
#include "config.h"
```

### 3. 选择延时定时器
在config.h中修改：
```c
#define DELAY_TIMER_SELECT 1  // 使用Timer1进行延时
```

## 编译说明

### 使用构建脚本（推荐）

#### SDCC环境
```bash
# 编译所有目标
./build.sh

# 清理编译文件
./build.sh clean

# 显示帮助
./build.sh help
```

#### Keil环境
```batch
REM 编译所有目标
build_keil.bat

REM 清理编译文件
build_keil.bat clean

REM 显示帮助
build_keil.bat help
```

### 手动编译

#### SDCC编译
```bash
# 在VSCode中按F7或使用命令
sdcc -mmcs51 -I./inc src/main.c lib/*.c -o build/Debug/main.ihx
```

#### Keil编译
在Keil uVision中按F7或使用菜单Project -> Build Target

### 编译输出

构建脚本会在 `build/hex/` 目录下生成以下文件：

- `main.hex` - 主程序
- `gpio_demo.hex` - GPIO演示程序
- `uart_demo.hex` - 串口演示程序
- `timer_demo.hex` - 定时器演示程序

## 下载说明

### 使用STC-ISP
1. 连接单片机到电脑
2. 打开STC-ISP软件
3. 选择对应的hex文件下载

### 使用stcflash.py
```bash
python tools/stcflash.py build/Debug/main.ihx
```

## 注意事项

1. **时钟配置**: 确保FOSC设置与实际使用的晶振频率一致
2. **定时器冲突**: 如果使用Timer1作为延时定时器，不能同时用于串口波特率发生器
3. **编译器选择**: 不同编译器生成的代码可能有细微差异，建议在目标环境中测试

## 更新日志

### v2.0 (当前版本)
- 添加FOSC动态配置支持
- 添加UART波特率动态配置
- 添加延时定时器选择功能
- 添加Timer工作模式枚举定义
- 添加Keil C51编译器兼容性
- 添加多目标编译支持（每个demo独立编译）
- 添加构建脚本（build.sh和build_keil.bat）
- 优化代码结构和注释
- 修复编译器兼容性问题

### v1.0
- 基础SDCC模板项目
- 基本的GPIO、UART、Timer功能
