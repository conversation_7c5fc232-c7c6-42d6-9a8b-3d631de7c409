/*************************************
 *
 *        89c52 sdcc 模板项目
 *
 * CPU: 89C52
 * FREQ: 12MHz
 *
 * ***********************************
*/

// 在包含config.h前可以覆盖默认配置
// #define FOSC 11059200L
// #define UART_BAUD 115200

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "../inc/config.h"
#include "../lib/uart.h"
#include "../lib/delay.h"
#include "../lib/gpio.h"
#include "../lib/timer.h"

// 取消注释以下行来选择要运行的示例
// #define RUN_GPIO_DEMO
// #define RUN_UART_DEMO
// #define RUN_TIMER_DEMO

// 示例函数声明
#ifdef RUN_GPIO_DEMO
extern void GPIO_Demo(void);
#endif

#ifdef RUN_UART_DEMO
extern void UART_Demo(void);
#endif

#ifdef RUN_TIMER_DEMO
extern void TIMER_Demo(void);
#endif

void main()
{
#if UART_ENABLE
    UART_Init();
    printf("System Started!\r\n");
#endif

    while (1)
    {
#ifdef __SDCC
        P0_0 = !P0_0;
        delay_ms_loop(1000);
        P0_0 = !P0_0;
#else
        P00 = !P00;
        delay_ms_loop(1000);
        P00 = !P00;
#endif
        delay_ms_timer(1000);

#if UART_ENABLE && UART_INPUT
        if(RI) {
            int c = getchar();
            printf("Received: %c\r\n", c);
        }
#endif
    }
}
