#include "uart.h"

/**
 * @brief 初始化串口
 */
void UART_Init(void)
{
#if UART_ENABLE
    SCON = 0x50;        // 模式1，允许接收
    TMOD &= 0x0F;       // 清除定时器1模式位
    TMOD |= 0x20;       // 定时器1，模式2
    
    // 根据波特率计算定时器初值
    // 对于12MHz晶振，9600波特率，TH1=0xFD
    TH1 = 0xFD;         // 9600bps @12MHz
    TL1 = TH1;
    ES = 1;             // 允许串口中断
    TR1 = 1;            // 启动定时器1
#endif
}

/**
 * @brief 发送一个字节
 * @param byte 要发送的字节
 */
void UART_SendByte(unsigned char byte)
{
#if UART_ENABLE
    SBUF = byte;
    while(!TI);
    TI = 0;
#endif
}

/**
 * @brief 发送字符串
 * @param str 要发送的字符串
 */
void UART_SendString(char *str)
{
#if UART_ENABLE
    while(*str) {
        UART_SendByte(*str++);
    }
#endif
}

#if UART_INPUT
/**
 * @brief 接收一个字节(阻塞)
 * @return 接收到的字节
 */
unsigned char UART_ReceiveByte(void)
{
    unsigned char temp;
    while(!RI);
    temp = SBUF;
    RI = 0;
    return temp;
}
#endif

/* 重定向putchar用于printf输出 */
int putchar(int c)
{
#if UART_ENABLE
    SBUF = c;
    while(!TI);
    TI = 0;
#endif
    return c;
}

#if UART_ENABLE && UART_INPUT
/* 重定向getchar用于scanf输入 */
int getchar(void)
{
    while(!RI);
    RI = 0;
    return SBUF;
}
#endif