#include "uart.h"

/**
 * @brief 初始化串口
 */
void UART_Init(void)
{
    unsigned char th1_value;

    SCON = 0x50;        // 模式1，允许接收
    TMOD &= 0x0F;       // 清除定时器1模式位
    TMOD |= 0x20;       // 定时器1，模式2（8位自动重装）

    // 根据FOSC和UART_BAUD动态计算定时器初值
    // 公式：TH1 = 256 - (FOSC / (32 * UART_BAUD))
    // 对于模式2，定时器1用作波特率发生器
    th1_value = 256 - (unsigned char)(FOSC / (32UL * UART_BAUD));

    TH1 = th1_value;
    TL1 = th1_value;
    ES = 1;             // 允许串口中断
    TR1 = 1;            // 启动定时器1
}

/**
 * @brief 发送一个字节
 * @param byte 要发送的字节
 */
void UART_SendByte(unsigned char byte)
{
    SBUF = byte;
    while(!TI);
    TI = 0;
}

/**
 * @brief 发送字符串
 * @param str 要发送的字符串
 */
void UART_SendString(char *str)
{
    while(*str) {
        UART_SendByte(*str++);
    }
}

/**
 * @brief 接收一个字节(阻塞)
 * @return 接收到的字节
 */
unsigned char UART_ReceiveByte(void)
{
    unsigned char temp;
    while(!RI);
    temp = SBUF;
    RI = 0;
    return temp;
}

/* 重定向putchar用于printf输出 */
#if ENABLE_UART_DEBUG
int putchar(int c)
{
    UART_SendByte(c);
    return c;
}
#endif
