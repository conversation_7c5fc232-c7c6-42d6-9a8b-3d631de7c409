/*************************************
 *
 *        89c52 sdcc 模板项目
 *
 * CPU: 89C52
 * FREQ: 12MHz
 *
 * ***********************************
*/

// 在包含config.h前可以覆盖默认配置
// #define FOSC 11059200L
// #define UART_BAUD 115200

#ifdef __SDCC
#include <mcs51/8052.h>
#else
#include <reg52.h>
#endif
#include "config.h"
#include "uart.h"
#include "delay.h"
#include "gpio.h"
#include "timer.h"


void main()
{
#if ENABLE_UART_DEBUG
    UART_Init();
    printf("System Started!\r\n");
#endif

    while (1)
    {
#ifdef __SDCC
        P0_0 = !P0_0;
        delay_ms_loop(1000);
        P0_0 = !P0_0;
#else
        P00 = !P00;
        delay_ms_loop(1000);
        P00 = !P00;
#endif
        delay_ms_timer(1000);
    }
}
